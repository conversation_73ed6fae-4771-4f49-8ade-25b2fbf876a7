package com.jcloud.admin.service.impl;

import cn.hutool.core.util.StrUtil;
import com.jcloud.admin.service.SysDeptService;
import com.jcloud.common.constant.CommonConstants;
import com.jcloud.common.dto.DeptCreateRequest;
import com.jcloud.common.dto.DeptQueryRequest;
import com.jcloud.common.dto.DeptUpdateRequest;
import com.jcloud.common.dto.SysDeptTreeNode;
import com.jcloud.common.entity.SysDept;
import com.jcloud.common.entity.SysUser;
import com.jcloud.common.entity.SysUserDept;
import com.jcloud.common.exception.BusinessException;
import com.jcloud.common.mapper.SysDeptMapper;
import com.jcloud.common.mapper.SysUserDeptMapper;
import com.jcloud.common.page.PageResult;
import com.jcloud.common.result.ResultCode;
import com.jcloud.common.service.impl.BaseServiceImpl;
import com.jcloud.common.util.SecurityUtils;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 部门服务实现类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SysDeptServiceImpl extends BaseServiceImpl<SysDeptMapper, SysDept> implements SysDeptService {
    
    private final SysUserDeptMapper userDeptMapper;
    
    @Override
    public PageResult<SysDept> pageDepts(DeptQueryRequest queryRequest) {
        QueryWrapper queryWrapper = getQueryWrapper();
        
        // 构建查询条件
        // 优先使用keyword进行OR查询
        if (StrUtil.isNotBlank(queryRequest.getKeyword())) {
            String keyword = queryRequest.getKeyword().trim();
            queryWrapper.where(SysDept::getDeptCode).like(keyword).or(SysDept::getDeptName).like(keyword);
            log.debug("使用关键词搜索: {}", keyword);
        } else {
            // 如果没有keyword，则使用具体字段查询
            if (StrUtil.isNotBlank(queryRequest.getDeptCode())) {
                queryWrapper.like(SysDept::getDeptCode, queryRequest.getDeptCode());
            }
            if (StrUtil.isNotBlank(queryRequest.getDeptName())) {
                queryWrapper.like(SysDept::getDeptName, queryRequest.getDeptName());
            }
        }
        if (queryRequest.getParentId() != null) {
            queryWrapper.eq(SysDept::getParentId, queryRequest.getParentId());
        }
        if (queryRequest.getLeaderId() != null) {
            queryWrapper.eq(SysDept::getLeaderId, queryRequest.getLeaderId());
        }
        if (StrUtil.isNotBlank(queryRequest.getPhone())) {
            queryWrapper.like(SysDept::getPhone, queryRequest.getPhone());
        }
        if (StrUtil.isNotBlank(queryRequest.getEmail())) {
            queryWrapper.like(SysDept::getEmail, queryRequest.getEmail());
        }
        if (queryRequest.getStatus() != null) {
            queryWrapper.eq(SysDept::getStatus, queryRequest.getStatus());
        }
        if (queryRequest.getCreateTimeStart() != null) {
            queryWrapper.ge(SysDept::getCreateTime, queryRequest.getCreateTimeStart());
        }
        if (queryRequest.getCreateTimeEnd() != null) {
            queryWrapper.le(SysDept::getCreateTime, queryRequest.getCreateTimeEnd());
        }
        
        // 排序
        queryWrapper.orderBy(SysDept::getSortOrder, true)
                   .orderBy(SysDept::getCreateTime, false);
        
        // 分页查询
        Page<SysDept> page = Page.of(queryRequest.getPageNum(), queryRequest.getPageSize());
        Page<SysDept> pageResult = baseMapper.paginate(page, queryWrapper);
        
        return PageResult.of(pageResult.getRecords(), pageResult.getTotalRow(), 
                           queryRequest.getPageNum(), queryRequest.getPageSize());
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createDept(DeptCreateRequest createRequest) {
        Long tenantId = SecurityUtils.getTenantId();
        if (tenantId == null) {
            throw new BusinessException(ResultCode.UNAUTHORIZED, "无法获取租户信息");
        }
        
        // 检查父部门是否存在
        if (createRequest.getParentId() != 0) {
            SysDept parentDept = getById(createRequest.getParentId());
            if (parentDept == null) {
                throw new BusinessException(ResultCode.NOT_FOUND, "父部门不存在");
            }
        }
        
        // 检查部门编码是否存在
        if (isDeptCodeExists(createRequest.getDeptCode(), null)) {
            throw new BusinessException(ResultCode.CONFLICT, "部门编码已存在");
        }
        
        // 检查部门名称是否存在（同级部门下）
        if (isDeptNameExists(createRequest.getDeptName(), createRequest.getParentId(), null)) {
            throw new BusinessException(ResultCode.CONFLICT, "同级部门下部门名称已存在");
        }
        
        // 创建部门
        SysDept dept = new SysDept();
        BeanUtils.copyProperties(createRequest, dept);
        setBaseInfo(dept, false);
        
        boolean success = save(dept);
        if (!success) {
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "创建部门失败");
        }
        
        log.info("创建部门成功，部门编码：{}，部门名称：{}", dept.getDeptCode(), dept.getDeptName());
        return true;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateDept(DeptUpdateRequest updateRequest) {
        SysDept existingDept = getById(updateRequest.getId());
        if (existingDept == null) {
            throw new BusinessException(ResultCode.NOT_FOUND, "部门不存在");
        }
        
        // 检查父部门是否存在（如果要更改父部门）
        if (updateRequest.getParentId() != null && updateRequest.getParentId() != 0) {
            SysDept parentDept = getById(updateRequest.getParentId());
            if (parentDept == null) {
                throw new BusinessException(ResultCode.NOT_FOUND, "父部门不存在");
            }
            
            // 检查是否形成循环引用
            if (isCircularReference(updateRequest.getId(), updateRequest.getParentId())) {
                throw new BusinessException(ResultCode.BAD_REQUEST, "不能将部门移动到自己的子部门下");
            }
        }
        
        // 检查部门名称是否存在（同级部门下，排除当前部门）
        Long parentId = updateRequest.getParentId() != null ? updateRequest.getParentId() : existingDept.getParentId();
        if (StrUtil.isNotBlank(updateRequest.getDeptName()) && 
            isDeptNameExists(updateRequest.getDeptName(), parentId, updateRequest.getId())) {
            throw new BusinessException(ResultCode.CONFLICT, "同级部门下部门名称已存在");
        }
        
        // 更新部门信息
        SysDept dept = new SysDept();
        BeanUtils.copyProperties(updateRequest, dept);
        setBaseInfo(dept, true);
        
        boolean success = updateById(dept);
        if (!success) {
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "更新部门失败");
        }
        
        log.info("更新部门成功，部门ID：{}，部门名称：{}", dept.getId(), dept.getDeptName());
        return true;
    }
    
    @Override
    public SysDept getDeptByCode(String deptCode) {
        Long tenantId = SecurityUtils.getTenantId();
        if (tenantId == null) {
            throw new BusinessException(ResultCode.UNAUTHORIZED, "无法获取租户信息");
        }
        return baseMapper.selectByDeptCode(deptCode, tenantId);
    }
    
    @Override
    public List<SysDept> getDeptsByParentId(Long parentId) {
        Long tenantId = SecurityUtils.getTenantId();
        if (tenantId == null) {
            throw new BusinessException(ResultCode.UNAUTHORIZED, "无法获取租户信息");
        }
        return baseMapper.selectByParentId(parentId, tenantId);
    }
    
    @Override
    public List<SysDept> getDeptsByUserId(Long userId) {
        Long tenantId = SecurityUtils.getTenantId();
        if (tenantId == null) {
            throw new BusinessException(ResultCode.UNAUTHORIZED, "无法获取租户信息");
        }
        return baseMapper.selectDeptsByUserId(userId, tenantId);
    }
    
    @Override
    public List<SysUser> getUsersByDeptId(Long deptId) {
        Long tenantId = SecurityUtils.getTenantId();
        if (tenantId == null) {
            throw new BusinessException(ResultCode.UNAUTHORIZED, "无法获取租户信息");
        }
        return baseMapper.selectUsersByDeptId(deptId, tenantId);
    }
    
    @Override
    public boolean isDeptCodeExists(String deptCode, Long excludeDeptId) {
        Long tenantId = SecurityUtils.getTenantId();
        if (tenantId == null) {
            throw new BusinessException(ResultCode.UNAUTHORIZED, "无法获取租户信息");
        }
        return baseMapper.existsByDeptCode(deptCode, excludeDeptId, tenantId);
    }
    
    @Override
    public boolean isDeptNameExists(String deptName, Long parentId, Long excludeDeptId) {
        Long tenantId = SecurityUtils.getTenantId();
        if (tenantId == null) {
            throw new BusinessException(ResultCode.UNAUTHORIZED, "无法获取租户信息");
        }
        return baseMapper.existsByDeptNameAndParentId(deptName, parentId, excludeDeptId, tenantId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateDeptStatus(Long deptId, Integer status) {
        SysDept dept = getById(deptId);
        if (dept == null) {
            throw new BusinessException(ResultCode.NOT_FOUND, "部门不存在");
        }

        dept.setStatus(status);
        setBaseInfo(dept, true);

        boolean success = updateById(dept);
        if (success) {
            log.info("更新部门状态成功，部门ID：{}，状态：{}", deptId, status);
        }
        return success;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateDeptStatusBatch(List<Long> deptIds, Integer status) {
        if (deptIds == null || deptIds.isEmpty()) {
            return true;
        }

        for (Long deptId : deptIds) {
            updateDeptStatus(deptId, status);
        }

        log.info("批量更新部门状态成功，部门数量：{}，状态：{}", deptIds.size(), status);
        return true;
    }

    @Override
    public List<SysDept> getAllEnabledDepts() {
        Long tenantId = SecurityUtils.getTenantId();
        if (tenantId == null) {
            throw new BusinessException(ResultCode.UNAUTHORIZED, "无法获取租户信息");
        }
        return baseMapper.selectEnabledDeptsByTenantId(tenantId);
    }

    @Override
    public List<SysDept> buildDeptTree() {
        List<SysDept> allDepts = getAllEnabledDepts();
        return buildTreeWithChildren(allDepts, 0L);
    }

    @Override
    public int countChildDepts(Long parentId) {
        Long tenantId = SecurityUtils.getTenantId();
        if (tenantId == null) {
            throw new BusinessException(ResultCode.UNAUTHORIZED, "无法获取租户信息");
        }
        return baseMapper.countByParentId(parentId, tenantId);
    }

    @Override
    public int countUsersByDeptId(Long deptId) {
        Long tenantId = SecurityUtils.getTenantId();
        if (tenantId == null) {
            throw new BusinessException(ResultCode.UNAUTHORIZED, "无法获取租户信息");
        }
        return baseMapper.countUsersByDeptId(deptId, tenantId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteDept(Long deptId) {
        SysDept dept = getById(deptId);
        if (dept == null) {
            throw new BusinessException(ResultCode.NOT_FOUND, "部门不存在");
        }

        // 检查是否有子部门
        int childCount = countChildDepts(deptId);
        if (childCount > 0) {
            throw new BusinessException(ResultCode.BAD_REQUEST,
                    String.format("该部门下还有%d个子部门，无法删除", childCount));
        }

        // 检查是否有用户
        int userCount = countUsersByDeptId(deptId);
        if (userCount > 0) {
            throw new BusinessException(ResultCode.BAD_REQUEST,
                    String.format("该部门下还有%d个用户，无法删除", userCount));
        }

        // 删除用户部门关联
        Long tenantId = SecurityUtils.getTenantId();
        userDeptMapper.deleteByDeptId(deptId, tenantId);

        // 删除部门
        boolean success = removeById(deptId);
        if (success) {
            log.info("删除部门成功，部门ID：{}，部门名称：{}", deptId, dept.getDeptName());
        }
        return success;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteDeptsBatch(List<Long> deptIds) {
        if (deptIds == null || deptIds.isEmpty()) {
            return true;
        }

        for (Long deptId : deptIds) {
            deleteDept(deptId);
        }

        log.info("批量删除部门成功，部门数量：{}", deptIds.size());
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean moveDept(Long deptId, Long newParentId) {
        SysDept dept = getById(deptId);
        if (dept == null) {
            throw new BusinessException(ResultCode.NOT_FOUND, "部门不存在");
        }

        // 检查新父部门是否存在
        if (newParentId != 0) {
            SysDept parentDept = getById(newParentId);
            if (parentDept == null) {
                throw new BusinessException(ResultCode.NOT_FOUND, "父部门不存在");
            }
        }

        // 检查是否形成循环引用
        if (isCircularReference(deptId, newParentId)) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "不能将部门移动到自己的子部门下");
        }

        // 检查部门名称是否冲突
        if (isDeptNameExists(dept.getDeptName(), newParentId, deptId)) {
            throw new BusinessException(ResultCode.CONFLICT, "目标位置已存在同名部门");
        }

        // 更新父部门ID
        dept.setParentId(newParentId);
        setBaseInfo(dept, true);

        boolean success = updateById(dept);
        if (success) {
            log.info("移动部门成功，部门ID：{}，新父部门ID：{}", deptId, newParentId);
        }
        return success;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean assignUserToDepts(Long userId, List<Long> deptIds, Long primaryDeptId) {
        Long tenantId = SecurityUtils.getTenantId();
        if (tenantId == null) {
            throw new BusinessException(ResultCode.UNAUTHORIZED, "无法获取租户信息");
        }

        // 删除原有关联
        userDeptMapper.deleteByUserId(userId, tenantId);

        // 添加新的关联
        if (deptIds != null && !deptIds.isEmpty()) {
            Long currentUserId = SecurityUtils.getUserId();
            LocalDateTime now = LocalDateTime.now();

            List<SysUserDept> userDepts = deptIds.stream()
                    .map(deptId -> {
                        SysUserDept userDept = new SysUserDept();
                        userDept.setTenantId(tenantId);
                        userDept.setUserId(userId);
                        userDept.setDeptId(deptId);
                        userDept.setIsMain(Objects.equals(deptId, primaryDeptId) ? 1 : 0);
                        userDept.setCreateTime(now);
                        userDept.setCreateBy(currentUserId);
                        return userDept;
                    })
                    .collect(Collectors.toList());

            // 使用MyBatis-Flex BaseMapper提供的原生批量插入方法
            userDeptMapper.insertBatch(userDepts);
        }

        log.info("分配用户部门成功，用户ID：{}，部门数量：{}", userId,
                deptIds != null ? deptIds.size() : 0);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeUserFromDept(Long userId, Long deptId) {
        Long tenantId = SecurityUtils.getTenantId();
        if (tenantId == null) {
            throw new BusinessException(ResultCode.UNAUTHORIZED, "无法获取租户信息");
        }

        int count = userDeptMapper.deleteByUserIdAndDeptId(userId, deptId, tenantId);
        if (count > 0) {
            log.info("从部门移除用户成功，用户ID：{}，部门ID：{}", userId, deptId);
        }
        return count > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean setUserPrimaryDept(Long userId, Long deptId) {
        Long tenantId = SecurityUtils.getTenantId();
        if (tenantId == null) {
            throw new BusinessException(ResultCode.UNAUTHORIZED, "无法获取租户信息");
        }

        // 检查用户是否在该部门
        SysUserDept userDept = userDeptMapper.selectByUserIdAndDeptId(userId, deptId, tenantId);
        if (userDept == null) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "用户不在该部门中");
        }

        int count = userDeptMapper.updatePrimaryDept(userId, deptId, tenantId);
        if (count > 0) {
            log.info("设置用户主部门成功，用户ID：{}，部门ID：{}", userId, deptId);
        }
        return count > 0;
    }

    @Override
    public Long getUserPrimaryDeptId(Long userId) {
        Long tenantId = SecurityUtils.getTenantId();
        if (tenantId == null) {
            throw new BusinessException(ResultCode.UNAUTHORIZED, "无法获取租户信息");
        }
        return userDeptMapper.selectPrimaryDeptIdByUserId(userId, tenantId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean initSystemDepts() {
        log.info("开始初始化系统部门...");

        List<SysDept> systemDepts = getSystemDepts();

        for (SysDept dept : systemDepts) {
            // 检查部门是否已存在
            SysDept existingDept = getDeptByCode(dept.getDeptCode());
            if (existingDept == null) {
                // 部门不存在，创建新部门
                setBaseInfo(dept, false);
                save(dept);
                log.info("创建系统部门：{} - {}", dept.getDeptCode(), dept.getDeptName());
            } else {
                // 部门已存在，更新部门信息
                dept.setId(existingDept.getId());
                setBaseInfo(dept, true);
                updateById(dept);
                log.info("更新系统部门：{} - {}", dept.getDeptCode(), dept.getDeptName());
            }
        }

        log.info("系统部门初始化完成，共处理{}个部门", systemDepts.size());
        return true;
    }

    /**
     * 构建部门树（带children字段）
     */
    private List<SysDept> buildTreeWithChildren(List<SysDept> depts, Long parentId) {
        List<SysDept> tree = new ArrayList<>();

        for (SysDept dept : depts) {
            if (Objects.equals(dept.getParentId(), parentId)) {
                // 递归构建子部门
                List<SysDept> children = buildTreeWithChildren(depts, dept.getId());

                // 创建部门树节点，包含children信息
                SysDeptTreeNode treeNode = new SysDeptTreeNode();
                BeanUtils.copyProperties(dept, treeNode);

                if (!children.isEmpty()) {
                    List<SysDeptTreeNode> childNodes = children.stream()
                        .map(child -> (SysDeptTreeNode) child)
                        .collect(Collectors.toList());
                    treeNode.setChildren(childNodes);
                }

                tree.add(treeNode);
            }
        }

        // 按排序字段排序
        tree.sort(Comparator.comparing(SysDept::getSortOrder,
                Comparator.nullsLast(Comparator.naturalOrder()))
                .thenComparing(SysDept::getCreateTime,
                Comparator.nullsLast(Comparator.naturalOrder())));

        return tree;
    }

    /**
     * 构建部门树（原方法，保持兼容性）
     */
    private List<SysDept> buildTree(List<SysDept> depts, Long parentId) {
        List<SysDept> tree = new ArrayList<>();

        for (SysDept dept : depts) {
            if (Objects.equals(dept.getParentId(), parentId)) {
                List<SysDept> children = buildTree(depts, dept.getId());
                tree.add(dept);
            }
        }

        // 按排序字段排序
        tree.sort(Comparator.comparing(SysDept::getSortOrder,
                Comparator.nullsLast(Comparator.naturalOrder()))
                .thenComparing(SysDept::getCreateTime,
                Comparator.nullsLast(Comparator.naturalOrder())));

        return tree;
    }

    /**
     * 检查是否形成循环引用
     */
    private boolean isCircularReference(Long deptId, Long parentId) {
        if (parentId == 0 || Objects.equals(deptId, parentId)) {
            return Objects.equals(deptId, parentId);
        }

        Long tenantId = SecurityUtils.getTenantId();
        if (tenantId == null) {
            return false;
        }

        // 获取所有子部门ID
        List<Long> childIds = baseMapper.selectChildIds(deptId, tenantId);
        return childIds.contains(parentId);
    }

    /**
     * 获取系统部门列表
     */
    private List<SysDept> getSystemDepts() {
        List<SysDept> depts = new ArrayList<>();

        // 总公司
        depts.add(createDept(0L, "COMPANY", "总公司", null, null, null, 1, 1, "总公司"));

        // 技术部
        depts.add(createDept(1L, "TECH", "技术部", null, null, null, 1, 11, "技术研发部门"));
        depts.add(createDept(2L, "TECH_DEV", "开发组", null, null, null, 1, 111, "软件开发组"));
        depts.add(createDept(2L, "TECH_TEST", "测试组", null, null, null, 1, 112, "软件测试组"));

        // 市场部
        depts.add(createDept(1L, "MARKET", "市场部", null, null, null, 1, 12, "市场营销部门"));
        depts.add(createDept(5L, "MARKET_SALES", "销售组", null, null, null, 1, 121, "销售团队"));

        // 人事部
        depts.add(createDept(1L, "HR", "人事部", null, null, null, 1, 13, "人力资源部门"));

        // 财务部
        depts.add(createDept(1L, "FINANCE", "财务部", null, null, null, 1, 14, "财务管理部门"));

        return depts;
    }

    /**
     * 创建部门对象
     */
    private SysDept createDept(Long parentId, String deptCode, String deptName, Long leaderId,
                              String phone, String email, Integer status, Integer sortOrder, String remark) {
        SysDept dept = new SysDept();
        dept.setParentId(parentId);
        dept.setDeptCode(deptCode);
        dept.setDeptName(deptName);
        dept.setLeaderId(leaderId);
        dept.setPhone(phone);
        dept.setEmail(email);
        dept.setStatus(status);
        dept.setSortOrder(sortOrder);
        dept.setRemark(remark);
        return dept;
    }
}
